package dashboard

import (
	"context"
	"sync"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/dashboard"
	"github.com/dsoplabs/dinbora-backend/internal/model/financialsheet"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	dashboardRepo "github.com/dsoplabs/dinbora-backend/internal/repository/dashboard"
	financialsheetService "github.com/dsoplabs/dinbora-backend/internal/service/financialsheet"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// isNotFoundError checks if the error is a NotFound error
func isNotFoundError(err error) bool {
	if domainErr, ok := err.(*errors.DomainError); ok {
		return domainErr.Kind() == errors.NotFound
	}
	return false
}

type service struct {
	Repository            dashboardRepo.Repository
	FinancialSheetService financialsheetService.Service
}

// New creates a new dashboard service
func New(repository dashboardRepo.Repository, financialSheetService financialsheetService.Service) Service {
	return &service{
		Repository:            repository,
		FinancialSheetService: financialSheetService,
	}
}

// FindFinancialMap implements the unified approach for dashboard data with fallback to legacy
func (s *service) FindFinancialMap(ctx context.Context, userID string) (*dashboard.FinancialMap, error) {
	// Try to fetch from unified collection first
	financialMap, err := s.Repository.FindFinancialMap(ctx, userID)
	if err != nil && !isNotFoundError(err) {
		return nil, err
	}

	// If found in unified collection, update with live data and return
	if financialMap != nil {
		return s.updateFinancialMapWithLiveData(ctx, financialMap)
	}

	// Fallback to legacy approach if not found in unified collection
	return s.findFinancialMapLegacy(ctx, userID)
}

// updateFinancialMapWithLiveData updates the financial map with live income data from financial sheet
func (s *service) updateFinancialMapWithLiveData(ctx context.Context, financialMap *dashboard.FinancialMap) (*dashboard.FinancialMap, error) {
	// Fetch live income sources from financial sheet transactions
	incomeSources, err := s.aggregateIncomeSourcesFromTransactions(ctx, financialMap.UserID)
	if err != nil {
		return nil, err
	}

	// Update income sources and monthly income
	financialMap.IncomeSources = make([]dashboard.IncomeSource, 0, len(incomeSources))
	var monthlyIncome monetary.Amount
	for _, source := range incomeSources {
		financialMap.IncomeSources = append(financialMap.IncomeSources, *source)
		monthlyIncome += source.MonthlyAmount
	}
	financialMap.MonthlyIncome = monthlyIncome

	// Create dynamic snapshot for current month
	var emergencyFundValue monetary.Amount
	if financialMap.EmergencyFund != nil {
		emergencyFundValue = financialMap.EmergencyFund.CurrentValue
	}

	currentSnapshot := dashboard.NetWorthSnapshot{
		UserID:             financialMap.UserID,
		Date:               time.Now(),
		EmergencyFundValue: emergencyFundValue,
		InvestmentsValue:   financialMap.TotalInvestments,
		AssetsValue:        financialMap.TotalAssets,
		TotalValue:         emergencyFundValue + financialMap.TotalInvestments + financialMap.TotalAssets,
	}

	// Add current snapshot to history if not already present for current month
	financialMap.NetWorthHistory = s.addCurrentSnapshotToHistory(financialMap.NetWorthHistory, currentSnapshot)

	return financialMap, nil
}

// addCurrentSnapshotToHistory adds the current snapshot to history if not already present for current month
func (s *service) addCurrentSnapshotToHistory(history []dashboard.NetWorthSnapshot, currentSnapshot dashboard.NetWorthSnapshot) []dashboard.NetWorthSnapshot {
	now := time.Now()
	currentYear, currentMonth, _ := now.Date()

	// Check if we already have a snapshot for the current month
	for i, snapshot := range history {
		snapshotYear, snapshotMonth, _ := snapshot.Date.Date()
		if snapshotYear == currentYear && snapshotMonth == currentMonth {
			// Update existing snapshot with current data
			history[i] = currentSnapshot
			return history
		}
	}

	// Add current snapshot to the end
	return append(history, currentSnapshot)
}

// findFinancialMapLegacy implements the original approach using separate collections
func (s *service) findFinancialMapLegacy(ctx context.Context, userID string) (*dashboard.FinancialMap, error) {
	// Use channels and goroutines for concurrent data fetching
	type result struct {
		incomeSources []*dashboard.IncomeSource
		emergencyFund *dashboard.EmergencyFund
		investments   []*dashboard.Investment
		assets        []*dashboard.Asset
		history       []*dashboard.NetWorthSnapshot
		err           error
	}

	resultChan := make(chan result, 1)

	go func() {
		var wg sync.WaitGroup
		var res result

		// Fetch historical snapshots (11 months)
		wg.Add(1)
		go func() {
			defer wg.Done()
			history, err := s.Repository.FindNetWorthHistory(ctx, userID, 11)
			if err != nil {
				res.err = err
				return
			}
			res.history = history
		}()

		// Fetch current live data concurrently
		wg.Add(4)

		// Fetch income sources from financial sheet transactions
		go func() {
			defer wg.Done()
			incomeSources, err := s.aggregateIncomeSourcesFromTransactions(ctx, userID)
			if err != nil {
				res.err = err
				return
			}
			res.incomeSources = incomeSources
		}()

		// Fetch emergency fund
		go func() {
			defer wg.Done()
			emergencyFund, err := s.Repository.FindEmergencyFund(ctx, userID)
			if err != nil && !isNotFoundError(err) {
				res.err = err
				return
			}
			res.emergencyFund = emergencyFund
		}()

		// Fetch investments
		go func() {
			defer wg.Done()
			investments, err := s.Repository.FindInvestments(ctx, userID)
			if err != nil {
				res.err = err
				return
			}
			res.investments = investments
		}()

		// Fetch assets
		go func() {
			defer wg.Done()
			assets, err := s.Repository.FindAssets(ctx, userID)
			if err != nil {
				res.err = err
				return
			}
			res.assets = assets
		}()

		wg.Wait()
		resultChan <- res
	}()

	res := <-resultChan
	if res.err != nil {
		return nil, res.err
	}

	// Calculate live totals
	var monthlyIncome, totalInvestments, totalAssets, emergencyFundValue monetary.Amount

	for _, source := range res.incomeSources {
		monthlyIncome += source.MonthlyAmount
	}

	for _, investment := range res.investments {
		totalInvestments += investment.CurrentValue
	}

	for _, asset := range res.assets {
		totalAssets += asset.Value
	}

	if res.emergencyFund != nil {
		emergencyFundValue = res.emergencyFund.CurrentValue
	}

	// Create dynamic snapshot for current month
	currentSnapshot := &dashboard.NetWorthSnapshot{
		UserID:             userID,
		Date:               time.Now(),
		EmergencyFundValue: emergencyFundValue,
		InvestmentsValue:   totalInvestments,
		AssetsValue:        totalAssets,
		TotalValue:         emergencyFundValue + totalInvestments + totalAssets,
	}

	// Combine historical data with current snapshot
	netWorthHistory := make([]dashboard.NetWorthSnapshot, 0, len(res.history)+1)
	for _, snapshot := range res.history {
		netWorthHistory = append(netWorthHistory, *snapshot)
	}
	netWorthHistory = append(netWorthHistory, *currentSnapshot)

	// Convert pointer slices to value slices
	incomeSources := make([]dashboard.IncomeSource, 0, len(res.incomeSources))
	for _, source := range res.incomeSources {
		incomeSources = append(incomeSources, *source)
	}

	investments := make([]dashboard.Investment, 0, len(res.investments))
	for _, investment := range res.investments {
		investments = append(investments, *investment)
	}

	assets := make([]dashboard.Asset, 0, len(res.assets))
	for _, asset := range res.assets {
		assets = append(assets, *asset)
	}

	// Assemble final FinancialMap
	financialMap := &dashboard.FinancialMap{
		UserID:           userID,
		MonthlyIncome:    monthlyIncome,
		EmergencyFund:    res.emergencyFund,
		TotalInvestments: totalInvestments,
		TotalAssets:      totalAssets,
		NetWorthHistory:  netWorthHistory,
		IncomeSources:    incomeSources,
		Investments:      investments,
		Assets:           assets,
	}

	// Save to unified collection for future use
	if err := financialMap.PrepareCreate(); err == nil {
		// Ignore errors when saving to unified collection as this is optimization
		_ = s.Repository.SaveFinancialMap(ctx, financialMap)
	}

	return financialMap, nil
}

// aggregateIncomeSourcesFromTransactions fetches income transactions and aggregates by money source
func (s *service) aggregateIncomeSourcesFromTransactions(ctx context.Context, userID string) ([]*dashboard.IncomeSource, error) {
	// Get current year and month
	now := time.Now()
	currentYear := now.Year()
	currentMonth := int(now.Month())

	// Fetch all income transactions for the current month
	transactions, err := s.FinancialSheetService.FindAllTransactions(ctx, userID, financialsheet.CategoryTypeIncome, currentYear, currentMonth)
	if err != nil {
		return nil, errors.New(errors.Service, "failed to fetch income transactions", errors.Internal, err)
	}

	// Aggregate transactions by money source (keep only the latest transaction per money source)
	sourceMap := make(map[financialsheet.MoneySource]*financialsheet.Transaction)

	for _, transaction := range transactions {
		existing, exists := sourceMap[transaction.MoneySource]
		if !exists || transaction.Date.After(existing.Date) {
			sourceMap[transaction.MoneySource] = transaction
		}
	}

	// Convert to IncomeSource structs
	var incomeSources []*dashboard.IncomeSource

	for moneySource, transaction := range sourceMap {
		// Get category to extract icon and name information
		category, err := s.FinancialSheetService.FindCategoryByIdentifier(ctx, transaction.Category)
		if err != nil {
			continue // Skip if category not found
		}

		// Get money source info from category
		moneySourceInfo := category.GetMoneySourceInfo(moneySource)

		incomeSource := &dashboard.IncomeSource{
			UserID:          userID,
			Name:            moneySourceInfo.Name,
			MonthlyAmount:   transaction.Value,
			MoneySource:     moneySource,
			Icon:            moneySourceInfo.Icon,
			BackgroundColor: string(category.Background),
			LastUpdated:     transaction.Date,
		}

		incomeSources = append(incomeSources, incomeSource)
	}

	return incomeSources, nil
}

// IncomeSource operations
func (s *service) CreateIncomeSource(ctx context.Context, userID string, name string, monthlyAmount monetary.Amount, moneySource byte) (*dashboard.IncomeSource, error) {
	incomeSource := &dashboard.IncomeSource{
		UserID:        userID,
		Name:          name,
		MonthlyAmount: monthlyAmount,
		MoneySource:   financialsheet.MoneySource(moneySource),
	}

	if err := incomeSource.PrepareCreate(); err != nil {
		return nil, err
	}

	if err := s.Repository.CreateIncomeSource(ctx, incomeSource); err != nil {
		return nil, err
	}

	// Sync with unified financial map (ignore errors as this is optimization)
	_ = s.syncUnifiedFinancialMap(ctx, userID)

	return incomeSource, nil
}

func (s *service) FindIncomeSources(ctx context.Context, userID string) ([]*dashboard.IncomeSource, error) {
	return s.Repository.FindIncomeSources(ctx, userID)
}

func (s *service) UpdateIncomeSource(ctx context.Context, id string, name string, monthlyAmount monetary.Amount) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.New(errors.Service, "invalid income source ID", errors.Validation, err)
	}

	incomeSource, err := s.Repository.FindIncomeSource(ctx, objectID)
	if err != nil {
		return err
	}

	incomeSource.Name = name
	incomeSource.MonthlyAmount = monthlyAmount

	if err := incomeSource.PrepareUpdate(); err != nil {
		return err
	}

	if err := s.Repository.UpdateIncomeSource(ctx, incomeSource); err != nil {
		return err
	}

	// Sync with unified financial map (ignore errors as this is optimization)
	_ = s.syncUnifiedFinancialMap(ctx, incomeSource.UserID)

	return nil
}

func (s *service) DeleteIncomeSource(ctx context.Context, id string) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.New(errors.Service, "invalid income source ID", errors.Validation, err)
	}

	// Get the income source to retrieve userID before deletion
	incomeSource, err := s.Repository.FindIncomeSource(ctx, objectID)
	if err != nil {
		return err
	}

	if err := s.Repository.DeleteIncomeSource(ctx, objectID); err != nil {
		return err
	}

	// Sync with unified financial map (ignore errors as this is optimization)
	_ = s.syncUnifiedFinancialMap(ctx, incomeSource.UserID)

	return nil
}

// EmergencyFund operations
func (s *service) FindEmergencyFund(ctx context.Context, userID string) (*dashboard.EmergencyFund, error) {
	return s.Repository.FindEmergencyFund(ctx, userID)
}

func (s *service) UpdateEmergencyFund(ctx context.Context, userID string, currentValue monetary.Amount) error {
	emergencyFund, err := s.Repository.FindEmergencyFund(ctx, userID)
	if err != nil {
		if isNotFoundError(err) {
			// Create new emergency fund if it doesn't exist
			emergencyFund = &dashboard.EmergencyFund{
				UserID:       userID,
				CurrentValue: currentValue,
				GoalValue:    0, // Default goal value
			}

			if err := emergencyFund.PrepareCreate(); err != nil {
				return err
			}

			if err := s.Repository.CreateEmergencyFund(ctx, emergencyFund); err != nil {
				return err
			}

			// Sync with unified financial map (ignore errors as this is optimization)
			_ = s.syncUnifiedFinancialMap(ctx, userID)

			return nil
		}
		return err
	}

	emergencyFund.CurrentValue = currentValue

	if err := emergencyFund.PrepareUpdate(); err != nil {
		return err
	}

	if err := s.Repository.UpdateEmergencyFund(ctx, emergencyFund); err != nil {
		return err
	}

	// Sync with unified financial map (ignore errors as this is optimization)
	_ = s.syncUnifiedFinancialMap(ctx, userID)

	return nil
}

func (s *service) UpdateEmergencyFundGoal(ctx context.Context, userID string, goalValue monetary.Amount) error {
	emergencyFund, err := s.Repository.FindEmergencyFund(ctx, userID)
	if err != nil {
		if isNotFoundError(err) {
			// Create new emergency fund if it doesn't exist
			emergencyFund = &dashboard.EmergencyFund{
				UserID:       userID,
				CurrentValue: 0, // Default current value
				GoalValue:    goalValue,
			}

			if err := emergencyFund.PrepareCreate(); err != nil {
				return err
			}

			if err := s.Repository.CreateEmergencyFund(ctx, emergencyFund); err != nil {
				return err
			}

			// Sync with unified financial map (ignore errors as this is optimization)
			_ = s.syncUnifiedFinancialMap(ctx, userID)

			return nil
		}
		return err
	}

	emergencyFund.GoalValue = goalValue

	if err := emergencyFund.PrepareUpdate(); err != nil {
		return err
	}

	if err := s.Repository.UpdateEmergencyFund(ctx, emergencyFund); err != nil {
		return err
	}

	// Sync with unified financial map (ignore errors as this is optimization)
	_ = s.syncUnifiedFinancialMap(ctx, userID)

	return nil
}

// Investment operations
func (s *service) CreateInvestment(ctx context.Context, userID string, name string, currentValue monetary.Amount) (*dashboard.Investment, error) {
	investment := &dashboard.Investment{
		UserID:       userID,
		Name:         name,
		CurrentValue: currentValue,
	}

	if err := investment.PrepareCreate(); err != nil {
		return nil, err
	}

	if err := s.Repository.CreateInvestment(ctx, investment); err != nil {
		return nil, err
	}

	return investment, nil
}

func (s *service) FindInvestments(ctx context.Context, userID string) ([]*dashboard.Investment, error) {
	return s.Repository.FindInvestments(ctx, userID)
}

func (s *service) UpdateInvestment(ctx context.Context, id string, name string, currentValue monetary.Amount) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.New(errors.Service, "invalid investment ID", errors.Validation, err)
	}

	investment, err := s.Repository.FindInvestment(ctx, objectID)
	if err != nil {
		return err
	}

	investment.Name = name
	investment.CurrentValue = currentValue

	if err := investment.PrepareUpdate(); err != nil {
		return err
	}

	return s.Repository.UpdateInvestment(ctx, investment)
}

func (s *service) DeleteInvestment(ctx context.Context, id string) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.New(errors.Service, "invalid investment ID", errors.Validation, err)
	}

	return s.Repository.DeleteInvestment(ctx, objectID)
}

// Asset operations
func (s *service) CreateAsset(ctx context.Context, userID string, description string, value monetary.Amount) (*dashboard.Asset, error) {
	asset := &dashboard.Asset{
		UserID:      userID,
		Description: description,
		Value:       value,
	}

	if err := asset.PrepareCreate(); err != nil {
		return nil, err
	}

	if err := s.Repository.CreateAsset(ctx, asset); err != nil {
		return nil, err
	}

	return asset, nil
}

func (s *service) FindAssets(ctx context.Context, userID string) ([]*dashboard.Asset, error) {
	return s.Repository.FindAssets(ctx, userID)
}

func (s *service) UpdateAsset(ctx context.Context, id string, description string, value monetary.Amount) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.New(errors.Service, "invalid asset ID", errors.Validation, err)
	}

	asset, err := s.Repository.FindAsset(ctx, objectID)
	if err != nil {
		return err
	}

	asset.Description = description
	asset.Value = value

	if err := asset.PrepareUpdate(); err != nil {
		return err
	}

	return s.Repository.UpdateAsset(ctx, asset)
}

func (s *service) DeleteAsset(ctx context.Context, id string) error {
	objectID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return errors.New(errors.Service, "invalid asset ID", errors.Validation, err)
	}

	return s.Repository.DeleteAsset(ctx, objectID)
}

// Snapshot operations
func (s *service) CreateMonthlySnapshot(ctx context.Context, userID string) error {
	// Fetch all current live data
	emergencyFund, err := s.Repository.FindEmergencyFund(ctx, userID)
	if err != nil && !isNotFoundError(err) {
		return err
	}

	investments, err := s.Repository.FindInvestments(ctx, userID)
	if err != nil {
		return err
	}

	assets, err := s.Repository.FindAssets(ctx, userID)
	if err != nil {
		return err
	}

	// Calculate totals
	var emergencyFundValue, investmentsValue, assetsValue monetary.Amount

	if emergencyFund != nil {
		emergencyFundValue = emergencyFund.CurrentValue
	}

	for _, investment := range investments {
		investmentsValue += investment.CurrentValue
	}

	for _, asset := range assets {
		assetsValue += asset.Value
	}

	// Create snapshot
	snapshot := &dashboard.NetWorthSnapshot{
		UserID:             userID,
		Date:               time.Now(),
		EmergencyFundValue: emergencyFundValue,
		InvestmentsValue:   investmentsValue,
		AssetsValue:        assetsValue,
	}

	if err := snapshot.PrepareCreate(); err != nil {
		return err
	}

	return s.Repository.SaveNetWorthSnapshot(ctx, snapshot)
}

func (s *service) FindNetWorthHistory(ctx context.Context, userID string, limit int) ([]*dashboard.NetWorthSnapshot, error) {
	return s.Repository.FindNetWorthHistory(ctx, userID, limit)
}

// syncUnifiedFinancialMap updates the unified financial map after component changes
func (s *service) syncUnifiedFinancialMap(ctx context.Context, userID string) error {
	// Try to get existing unified financial map
	financialMap, err := s.Repository.FindFinancialMap(ctx, userID)
	if err != nil && !isNotFoundError(err) {
		return err
	}

	// If not found, create a new one by fetching from legacy collections
	if financialMap == nil {
		_, err := s.findFinancialMapLegacy(ctx, userID)
		if err != nil {
			return err
		}
		return nil // Already saved in findFinancialMapLegacy
	}

	// Update existing financial map with current data from legacy collections
	// Fetch current data
	emergencyFund, err := s.Repository.FindEmergencyFund(ctx, userID)
	if err != nil && !isNotFoundError(err) {
		return err
	}

	investments, err := s.Repository.FindInvestments(ctx, userID)
	if err != nil {
		return err
	}

	assets, err := s.Repository.FindAssets(ctx, userID)
	if err != nil {
		return err
	}

	// Update financial map
	financialMap.EmergencyFund = emergencyFund

	// Convert pointer slices to value slices
	financialMap.Investments = make([]dashboard.Investment, 0, len(investments))
	for _, investment := range investments {
		financialMap.Investments = append(financialMap.Investments, *investment)
	}

	financialMap.Assets = make([]dashboard.Asset, 0, len(assets))
	for _, asset := range assets {
		financialMap.Assets = append(financialMap.Assets, *asset)
	}

	// Prepare for update (this will recalculate totals)
	if err := financialMap.PrepareUpdate(); err != nil {
		return err
	}

	// Update in database
	return s.Repository.UpdateFinancialMap(ctx, financialMap)
}
