package dashboard

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/model/dashboard"
)

// Reader interface defines read operations for dashboard entities
type Reader interface {
	// Unified FinancialMap operations
	FindFinancialMap(ctx context.Context, userID string) (*dashboard.FinancialMap, error)
}

// Writer interface defines write operations for dashboard entities
type Writer interface {
	// Unified FinancialMap operations
	SaveFinancialMap(ctx context.Context, financialMap *dashboard.FinancialMap) error
	UpdateFinancialMap(ctx context.Context, financialMap *dashboard.FinancialMap) error
}

// Repository interface combines Reader and Writer interfaces
type Repository interface {
	Reader
	Writer
}
