package dashboard

import (
	"context"
	"log"

	"github.com/dsoplabs/dinbora-backend/internal/errors"
	"github.com/dsoplabs/dinbora-backend/internal/model/dashboard"
	"github.com/dsoplabs/dinbora-backend/internal/repository"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

type mongoDB struct {
	financialMapCollection *mongo.Collection
}

// New creates a new dashboard repository with MongoDB implementation
func New(db *mongo.Database) Repository {
	repo := &mongoDB{
		financialMapCollection: db.Collection(repository.DASHBOARD_FINANCIAL_MAP_COLLECTION),
	}

	// Create indexes
	repo.createIndexes()

	return repo
}

// createIndexes creates necessary indexes for dashboard collections
func (m *mongoDB) createIndexes() {
	ctx := context.Background()

	// FinancialMap indexes (unified collection)
	_, err := m.financialMapCollection.Indexes().CreateOne(ctx, mongo.IndexModel{
		Keys:    bson.D{{Key: "userID", Value: 1}},
		Options: options.Index().SetUnique(true),
	})
	if err != nil {
		log.Printf("Warning: failed to create unique index on financial map userID field: %v", err)
	}
}

// Unified FinancialMap operations
func (m *mongoDB) FindFinancialMap(ctx context.Context, userID string) (*dashboard.FinancialMap, error) {
	var financialMap dashboard.FinancialMap
	err := m.financialMapCollection.FindOne(ctx, bson.M{"userID": userID}).Decode(&financialMap)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errors.Repository, "financial map not found", errors.NotFound, err)
		}
		return nil, errors.New(errors.Repository, "failed to find financial map", errors.Internal, err)
	}

	financialMap.ID = financialMap.ObjectID.Hex()
	return &financialMap, nil
}

func (m *mongoDB) SaveFinancialMap(ctx context.Context, financialMap *dashboard.FinancialMap) error {
	result, err := m.financialMapCollection.InsertOne(ctx, financialMap)
	if err != nil {
		return errors.New(errors.Repository, "failed to save financial map", errors.Internal, err)
	}

	financialMap.ObjectID = result.InsertedID.(primitive.ObjectID)
	financialMap.ID = financialMap.ObjectID.Hex()
	return nil
}

func (m *mongoDB) UpdateFinancialMap(ctx context.Context, financialMap *dashboard.FinancialMap) error {
	filter := bson.M{"userID": financialMap.UserID}
	update := bson.M{"$set": financialMap}

	result, err := m.financialMapCollection.UpdateOne(ctx, filter, update)
	if err != nil {
		return errors.New(errors.Repository, "failed to update financial map", errors.Internal, err)
	}

	if result.MatchedCount == 0 {
		return errors.New(errors.Repository, "financial map not found", errors.NotFound, nil)
	}

	return nil
}
