package dashboard

import (
	"context"

	"github.com/dsoplabs/dinbora-backend/internal/model/dashboard"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
)

// Service interface defines the dashboard service operations
type Service interface {
	// Main dashboard method
	FindFinancialMap(ctx context.Context, userID string) (*dashboard.FinancialMap, error)

	// Component update operations (work directly with unified FinancialMap)
	UpdateEmergencyFund(ctx context.Context, userID string, currentValue monetary.Amount) error
	UpdateEmergencyFundGoal(ctx context.Context, userID string, goalValue monetary.Amount) error

	AddInvestment(ctx context.Context, userID string, name string, currentValue monetary.Amount) error
	UpdateInvestment(ctx context.Context, userID string, investmentID string, name string, currentValue monetary.Amount) error
	RemoveInvestment(ctx context.Context, userID string, investmentID string) error

	AddAsset(ctx context.Context, userID string, description string, value monetary.Amount) error
	UpdateAsset(ctx context.Context, userID string, assetID string, description string, value monetary.Amount) error
	RemoveAsset(ctx context.Context, userID string, assetID string) error

	// Snapshot operations
	CreateMonthlySnapshot(ctx context.Context, userID string) error
}
