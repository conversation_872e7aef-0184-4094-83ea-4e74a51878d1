package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"time"

	"github.com/dsoplabs/dinbora-backend/internal/model/dashboard"
	"github.com/dsoplabs/dinbora-backend/internal/model/monetary"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const (
	// Legacy collections
	DASHBOARD_INCOME_SOURCES_COLLECTION      = "dashboard.income_sources"
	DASHBOARD_EMERGENCY_FUNDS_COLLECTION     = "dashboard.emergency_funds"
	DASHBOARD_INVESTMENTS_COLLECTION         = "dashboard.investments"
	DASHBOARD_ASSETS_COLLECTION              = "dashboard.assets"
	DASHBOARD_NET_WORTH_SNAPSHOTS_COLLECTION = "dashboard.net_worth_snapshots"
	
	// New unified collection
	DASHBOARD_FINANCIAL_MAP_COLLECTION = "dashboard.financialmap"
)

func main() {
	// Get database connection details from environment
	mongoURI := os.Getenv("DATABASE_URL")
	dbName := os.Getenv("DATABASE_NAME")

	if mongoURI == "" || dbName == "" {
		log.Fatal("DATABASE_URL and DATABASE_NAME environment variables must be set")
	}

	// Connect to MongoDB
	ctx := context.Background()
	client, err := mongo.Connect(ctx, options.Client().ApplyURI(mongoURI))
	if err != nil {
		log.Fatalf("Failed to connect to MongoDB: %v", err)
	}
	defer client.Disconnect(ctx)

	db := client.Database(dbName)

	// Run migration
	if err := migrateToUnifiedCollection(ctx, db); err != nil {
		log.Fatalf("Migration failed: %v", err)
	}

	log.Println("Migration completed successfully!")
}

func migrateToUnifiedCollection(ctx context.Context, db *mongo.Database) error {
	// Get all collections
	legacyCollections := map[string]*mongo.Collection{
		"income_sources":      db.Collection(DASHBOARD_INCOME_SOURCES_COLLECTION),
		"emergency_funds":     db.Collection(DASHBOARD_EMERGENCY_FUNDS_COLLECTION),
		"investments":         db.Collection(DASHBOARD_INVESTMENTS_COLLECTION),
		"assets":              db.Collection(DASHBOARD_ASSETS_COLLECTION),
		"net_worth_snapshots": db.Collection(DASHBOARD_NET_WORTH_SNAPSHOTS_COLLECTION),
	}
	
	unifiedCollection := db.Collection(DASHBOARD_FINANCIAL_MAP_COLLECTION)

	// Create index on unified collection
	_, err := unifiedCollection.Indexes().CreateOne(ctx, mongo.IndexModel{
		Keys:    bson.D{{Key: "userID", Value: 1}},
		Options: options.Index().SetUnique(true),
	})
	if err != nil {
		return fmt.Errorf("failed to create index on unified collection: %w", err)
	}

	// Get all unique user IDs from all collections
	userIDs, err := getAllUserIDs(ctx, legacyCollections)
	if err != nil {
		return fmt.Errorf("failed to get user IDs: %w", err)
	}

	log.Printf("Found %d unique users to migrate", len(userIDs))

	// Migrate each user's data
	migrated := 0
	skipped := 0
	
	for _, userID := range userIDs {
		// Check if user already exists in unified collection
		count, err := unifiedCollection.CountDocuments(ctx, bson.M{"userID": userID})
		if err != nil {
			return fmt.Errorf("failed to check existing user %s: %w", userID, err)
		}
		
		if count > 0 {
			log.Printf("User %s already exists in unified collection, skipping", userID)
			skipped++
			continue
		}

		// Migrate user data
		if err := migrateUserData(ctx, legacyCollections, unifiedCollection, userID); err != nil {
			log.Printf("Failed to migrate user %s: %v", userID, err)
			continue
		}
		
		migrated++
		if migrated%100 == 0 {
			log.Printf("Migrated %d users so far...", migrated)
		}
	}

	log.Printf("Migration summary: %d migrated, %d skipped", migrated, skipped)
	return nil
}

func getAllUserIDs(ctx context.Context, collections map[string]*mongo.Collection) ([]string, error) {
	userIDSet := make(map[string]bool)

	for collName, coll := range collections {
		cursor, err := coll.Find(ctx, bson.M{}, options.Find().SetProjection(bson.M{"userID": 1}))
		if err != nil {
			log.Printf("Warning: failed to query %s collection: %v", collName, err)
			continue
		}

		for cursor.Next(ctx) {
			var doc struct {
				UserID string `bson:"userID"`
			}
			if err := cursor.Decode(&doc); err != nil {
				log.Printf("Warning: failed to decode document from %s: %v", collName, err)
				continue
			}
			if doc.UserID != "" {
				userIDSet[doc.UserID] = true
			}
		}
		cursor.Close(ctx)
	}

	userIDs := make([]string, 0, len(userIDSet))
	for userID := range userIDSet {
		userIDs = append(userIDs, userID)
	}

	return userIDs, nil
}

func migrateUserData(ctx context.Context, legacyCollections map[string]*mongo.Collection, unifiedCollection *mongo.Collection, userID string) error {
	// Fetch all user data from legacy collections
	incomeSources, err := fetchIncomeSources(ctx, legacyCollections["income_sources"], userID)
	if err != nil {
		return fmt.Errorf("failed to fetch income sources: %w", err)
	}

	emergencyFund, err := fetchEmergencyFund(ctx, legacyCollections["emergency_funds"], userID)
	if err != nil {
		return fmt.Errorf("failed to fetch emergency fund: %w", err)
	}

	investments, err := fetchInvestments(ctx, legacyCollections["investments"], userID)
	if err != nil {
		return fmt.Errorf("failed to fetch investments: %w", err)
	}

	assets, err := fetchAssets(ctx, legacyCollections["assets"], userID)
	if err != nil {
		return fmt.Errorf("failed to fetch assets: %w", err)
	}

	netWorthHistory, err := fetchNetWorthHistory(ctx, legacyCollections["net_worth_snapshots"], userID)
	if err != nil {
		return fmt.Errorf("failed to fetch net worth history: %w", err)
	}

	// Calculate totals
	var monthlyIncome, totalInvestments, totalAssets monetary.Amount
	
	for _, source := range incomeSources {
		monthlyIncome += source.MonthlyAmount
	}
	
	for _, investment := range investments {
		totalInvestments += investment.CurrentValue
	}
	
	for _, asset := range assets {
		totalAssets += asset.Value
	}

	// Create unified financial map
	financialMap := &dashboard.FinancialMap{
		UserID:           userID,
		MonthlyIncome:    monthlyIncome,
		EmergencyFund:    emergencyFund,
		TotalInvestments: totalInvestments,
		TotalAssets:      totalAssets,
		NetWorthHistory:  netWorthHistory,
		IncomeSources:    incomeSources,
		Investments:      investments,
		Assets:           assets,
		CreatedAt:        time.Now(),
		UpdatedAt:        time.Now(),
	}

	// Insert into unified collection
	_, err = unifiedCollection.InsertOne(ctx, financialMap)
	if err != nil {
		return fmt.Errorf("failed to insert unified financial map: %w", err)
	}

	return nil
}
